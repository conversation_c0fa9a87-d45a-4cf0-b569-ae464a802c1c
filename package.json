{"name": "synapseai", "version": "1.0.0", "description": "SynapseAI - Production-ready AI orchestration platform", "private": true, "workspaces": ["packages/*", "apps/*"], "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "npm run dev --workspace=@synapseai/backend", "dev:frontend": "npm run dev --workspace=@synapseai/frontend", "build": "npm run build --workspaces", "test": "npm run test --workspaces", "lint": "eslint . --ext .ts,.tsx", "type-check": "tsc --noEmit", "clean": "rimraf node_modules packages/*/node_modules apps/*/node_modules packages/*/dist apps/*/dist"}, "devDependencies": {"@types/node": "^20.10.0", "@typescript-eslint/eslint-plugin": "^6.13.0", "@typescript-eslint/parser": "^6.13.0", "concurrently": "^8.2.2", "eslint": "^8.54.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.1", "prettier": "^3.1.0", "rimraf": "^5.0.5", "typescript": "^5.3.0"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}