import express from 'express'
import { createServer } from 'http'
import { Server as SocketIOServer } from 'socket.io'
import cors from 'cors'
import helmet from 'helmet'
import rateLimit from 'express-rate-limit'
import dotenv from 'dotenv'

import { logger } from '@/utils/logger'
import { errorHandler } from '@/middleware/error-handler'
import { authMiddleware } from '@/middleware/auth'
import { validateEnv } from '@/config/env'
import { initializeDatabase } from '@/config/database'
import { initializeRedis } from '@/config/redis'
import { setupWebSocket } from '@/services/websocket'

// Routes
import authRoutes from '@/routes/auth'
import agentRoutes from '@/routes/agents'
import toolRoutes from '@/routes/tools'
import providerRoutes from '@/routes/providers'
import sessionRoutes from '@/routes/sessions'

// Load environment variables
dotenv.config()

async function startServer() {
  try {
    // Validate environment variables
    const env = validateEnv()
    
    // Initialize external services
    await initializeDatabase()
    await initializeRedis()
    
    // Create Express app
    const app = express()
    const server = createServer(app)
    
    // Initialize Socket.IO
    const io = new SocketIOServer(server, {
      cors: {
        origin: env.CORS_ORIGIN,
        methods: ['GET', 'POST'],
        credentials: true
      },
      transports: ['websocket', 'polling']
    })
    
    // Setup WebSocket handlers
    setupWebSocket(io)
    
    // Security middleware
    app.use(helmet())
    app.use(cors({
      origin: env.CORS_ORIGIN,
      credentials: true
    }))
    
    // Rate limiting
    const limiter = rateLimit({
      windowMs: env.RATE_LIMIT_WINDOW_MS,
      max: env.RATE_LIMIT_MAX_REQUESTS,
      message: 'Too many requests from this IP'
    })
    app.use('/api/', limiter)
    
    // Body parsing
    app.use(express.json({ limit: '10mb' }))
    app.use(express.urlencoded({ extended: true }))
    
    // Health check
    app.get('/health', (req, res) => {
      res.json({ 
        status: 'ok', 
        timestamp: new Date().toISOString(),
        version: process.env.npm_package_version || '1.0.0'
      })
    })
    
    // API routes
    app.use('/api/auth', authRoutes)
    app.use('/api/agents', authMiddleware, agentRoutes)
    app.use('/api/tools', authMiddleware, toolRoutes)
    app.use('/api/providers', authMiddleware, providerRoutes)
    app.use('/api/sessions', authMiddleware, sessionRoutes)
    
    // Error handling
    app.use(errorHandler)
    
    // Start server
    const port = env.PORT
    server.listen(port, () => {
      logger.info(`🚀 SynapseAI Backend running on port ${port}`)
      logger.info(`📊 Environment: ${env.NODE_ENV}`)
      logger.info(`🔗 CORS Origin: ${env.CORS_ORIGIN}`)
    })
    
    // Graceful shutdown
    process.on('SIGTERM', () => {
      logger.info('SIGTERM received, shutting down gracefully')
      server.close(() => {
        logger.info('Process terminated')
        process.exit(0)
      })
    })
    
  } catch (error) {
    logger.error('Failed to start server:', error)
    process.exit(1)
  }
}

startServer()
