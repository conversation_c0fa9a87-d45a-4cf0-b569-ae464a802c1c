import { z } from 'zod'

// Base UAUI Event Schema
export const UAUIEventSchema = z.object({
  id: z.string().uuid(),
  type: z.string(),
  timestamp: z.string().datetime(),
  sessionId: z.string(),
  userId: z.string().optional(),
  agentId: z.string().optional(),
  data: z.record(z.any())
})

// Agent Lifecycle Events
export const AgentStartEventSchema = UAUIEventSchema.extend({
  type: z.literal('agent.start'),
  data: z.object({
    agentId: z.string(),
    config: z.record(z.any()),
    context: z.record(z.any()).optional()
  })
})

export const AgentStopEventSchema = UAUIEventSchema.extend({
  type: z.literal('agent.stop'),
  data: z.object({
    agentId: z.string(),
    reason: z.enum(['completed', 'error', 'timeout', 'user_stop']),
    result: z.record(z.any()).optional()
  })
})

export const AgentThinkingEventSchema = UAUIEventSchema.extend({
  type: z.literal('agent.thinking'),
  data: z.object({
    agentId: z.string(),
    step: z.string(),
    reasoning: z.string().optional()
  })
})

// Tool Execution Events
export const ToolCallEventSchema = UAUIEventSchema.extend({
  type: z.literal('tool.call'),
  data: z.object({
    toolId: z.string(),
    toolName: z.string(),
    input: z.record(z.any()),
    agentId: z.string()
  })
})

export const ToolResponseEventSchema = UAUIEventSchema.extend({
  type: z.literal('tool.response'),
  data: z.object({
    toolId: z.string(),
    toolName: z.string(),
    output: z.record(z.any()),
    success: z.boolean(),
    error: z.string().optional(),
    executionTime: z.number()
  })
})

// Provider Communication Events
export const ProviderRequestEventSchema = UAUIEventSchema.extend({
  type: z.literal('provider.request'),
  data: z.object({
    providerId: z.string(),
    providerType: z.enum(['openai', 'anthropic', 'google', 'custom']),
    model: z.string(),
    messages: z.array(z.object({
      role: z.enum(['system', 'user', 'assistant']),
      content: z.string()
    })),
    config: z.record(z.any()).optional()
  })
})

export const ProviderResponseEventSchema = UAUIEventSchema.extend({
  type: z.literal('provider.response'),
  data: z.object({
    providerId: z.string(),
    response: z.string(),
    usage: z.object({
      promptTokens: z.number(),
      completionTokens: z.number(),
      totalTokens: z.number()
    }).optional(),
    finishReason: z.string().optional(),
    responseTime: z.number()
  })
})

// Message Events
export const UserMessageEventSchema = UAUIEventSchema.extend({
  type: z.literal('message.user'),
  data: z.object({
    content: z.string(),
    metadata: z.record(z.any()).optional()
  })
})

export const AgentMessageEventSchema = UAUIEventSchema.extend({
  type: z.literal('message.agent'),
  data: z.object({
    content: z.string(),
    agentId: z.string(),
    confidence: z.number().min(0).max(1).optional(),
    metadata: z.record(z.any()).optional()
  })
})

// System Events
export const SystemEventSchema = UAUIEventSchema.extend({
  type: z.literal('system.event'),
  data: z.object({
    level: z.enum(['info', 'warn', 'error']),
    message: z.string(),
    details: z.record(z.any()).optional()
  })
})

export const ErrorEventSchema = UAUIEventSchema.extend({
  type: z.literal('system.error'),
  data: z.object({
    error: z.string(),
    code: z.string().optional(),
    stack: z.string().optional(),
    context: z.record(z.any()).optional()
  })
})

// Session Events
export const SessionStartEventSchema = UAUIEventSchema.extend({
  type: z.literal('session.start'),
  data: z.object({
    sessionId: z.string(),
    userId: z.string(),
    config: z.record(z.any()).optional()
  })
})

export const SessionEndEventSchema = UAUIEventSchema.extend({
  type: z.literal('session.end'),
  data: z.object({
    sessionId: z.string(),
    duration: z.number(),
    reason: z.enum(['completed', 'timeout', 'error', 'user_end'])
  })
})

// Union of all event types
export const UAUIEventUnionSchema = z.discriminatedUnion('type', [
  AgentStartEventSchema,
  AgentStopEventSchema,
  AgentThinkingEventSchema,
  ToolCallEventSchema,
  ToolResponseEventSchema,
  ProviderRequestEventSchema,
  ProviderResponseEventSchema,
  UserMessageEventSchema,
  AgentMessageEventSchema,
  SystemEventSchema,
  ErrorEventSchema,
  SessionStartEventSchema,
  SessionEndEventSchema
])

// TypeScript types
export type UAUIEvent = z.infer<typeof UAUIEventSchema>
export type AgentStartEvent = z.infer<typeof AgentStartEventSchema>
export type AgentStopEvent = z.infer<typeof AgentStopEventSchema>
export type AgentThinkingEvent = z.infer<typeof AgentThinkingEventSchema>
export type ToolCallEvent = z.infer<typeof ToolCallEventSchema>
export type ToolResponseEvent = z.infer<typeof ToolResponseEventSchema>
export type ProviderRequestEvent = z.infer<typeof ProviderRequestEventSchema>
export type ProviderResponseEvent = z.infer<typeof ProviderResponseEventSchema>
export type UserMessageEvent = z.infer<typeof UserMessageEventSchema>
export type AgentMessageEvent = z.infer<typeof AgentMessageEventSchema>
export type SystemEvent = z.infer<typeof SystemEventSchema>
export type ErrorEvent = z.infer<typeof ErrorEventSchema>
export type SessionStartEvent = z.infer<typeof SessionStartEventSchema>
export type SessionEndEvent = z.infer<typeof SessionEndEventSchema>
export type UAUIEventUnion = z.infer<typeof UAUIEventUnionSchema>

// Event factory functions
export class UAUIEventFactory {
  static createAgentStart(sessionId: string, agentId: string, config: Record<string, any>): AgentStartEvent {
    return {
      id: crypto.randomUUID(),
      type: 'agent.start',
      timestamp: new Date().toISOString(),
      sessionId,
      agentId,
      data: { agentId, config }
    }
  }

  static createToolCall(sessionId: string, agentId: string, toolId: string, toolName: string, input: Record<string, any>): ToolCallEvent {
    return {
      id: crypto.randomUUID(),
      type: 'tool.call',
      timestamp: new Date().toISOString(),
      sessionId,
      agentId,
      data: { toolId, toolName, input, agentId }
    }
  }

  static createUserMessage(sessionId: string, userId: string, content: string): UserMessageEvent {
    return {
      id: crypto.randomUUID(),
      type: 'message.user',
      timestamp: new Date().toISOString(),
      sessionId,
      userId,
      data: { content }
    }
  }

  static createSystemError(sessionId: string, error: string, context?: Record<string, any>): ErrorEvent {
    return {
      id: crypto.randomUUID(),
      type: 'system.error',
      timestamp: new Date().toISOString(),
      sessionId,
      data: { error, context }
    }
  }
}
