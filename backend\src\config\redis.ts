import { createClient, RedisClientType } from 'redis'
import { logger } from '@/utils/logger'
import { getEnv } from './env'

let redisClient: RedisClientType
let redisSubscriber: RedisClientType
let redisPublisher: RedisClientType

export async function initializeRedis(): Promise<void> {
  try {
    const env = getEnv()
    
    // Main Redis client
    redisClient = createClient({
      url: env.REDIS_URL,
      socket: {
        reconnectStrategy: (retries) => Math.min(retries * 50, 500)
      }
    })
    
    // Subscriber client for pub/sub
    redisSubscriber = createClient({
      url: env.REDIS_URL,
      socket: {
        reconnectStrategy: (retries) => Math.min(retries * 50, 500)
      }
    })
    
    // Publisher client for pub/sub
    redisPublisher = createClient({
      url: env.REDIS_URL,
      socket: {
        reconnectStrategy: (retries) => Math.min(retries * 50, 500)
      }
    })
    
    // Error handlers
    redisClient.on('error', (err) => logger.error('Redis Client Error:', err))
    redisSubscriber.on('error', (err) => logger.error('Redis Subscriber Error:', err))
    redisPublisher.on('error', (err) => logger.error('Redis Publisher Error:', err))
    
    // Connect all clients
    await Promise.all([
      redisClient.connect(),
      redisSubscriber.connect(),
      redisPublisher.connect()
    ])
    
    logger.info('✅ Redis connected successfully')
    
    // Test the connection
    await redisClient.ping()
    logger.info('✅ Redis health check passed')
    
  } catch (error) {
    logger.error('❌ Redis connection failed:', error)
    throw new Error('Failed to connect to Redis')
  }
}

export function getRedisClient(): RedisClientType {
  if (!redisClient) {
    throw new Error('Redis client not initialized. Call initializeRedis() first.')
  }
  return redisClient
}

export function getRedisSubscriber(): RedisClientType {
  if (!redisSubscriber) {
    throw new Error('Redis subscriber not initialized. Call initializeRedis() first.')
  }
  return redisSubscriber
}

export function getRedisPublisher(): RedisClientType {
  if (!redisPublisher) {
    throw new Error('Redis publisher not initialized. Call initializeRedis() first.')
  }
  return redisPublisher
}

export async function disconnectRedis(): Promise<void> {
  try {
    await Promise.all([
      redisClient?.quit(),
      redisSubscriber?.quit(),
      redisPublisher?.quit()
    ])
    logger.info('✅ Redis disconnected successfully')
  } catch (error) {
    logger.error('❌ Redis disconnection failed:', error)
  }
}

// Session management utilities
export class SessionManager {
  private static readonly SESSION_PREFIX = 'session:'
  private static readonly USER_SESSIONS_PREFIX = 'user_sessions:'
  
  static async setSession(sessionId: string, data: any, ttl?: number): Promise<void> {
    const client = getRedisClient()
    const key = `${this.SESSION_PREFIX}${sessionId}`
    
    if (ttl) {
      await client.setEx(key, ttl, JSON.stringify(data))
    } else {
      await client.set(key, JSON.stringify(data))
    }
  }
  
  static async getSession(sessionId: string): Promise<any | null> {
    const client = getRedisClient()
    const key = `${this.SESSION_PREFIX}${sessionId}`
    const data = await client.get(key)
    
    return data ? JSON.parse(data) : null
  }
  
  static async deleteSession(sessionId: string): Promise<void> {
    const client = getRedisClient()
    const key = `${this.SESSION_PREFIX}${sessionId}`
    await client.del(key)
  }
  
  static async addUserSession(userId: string, sessionId: string): Promise<void> {
    const client = getRedisClient()
    const key = `${this.USER_SESSIONS_PREFIX}${userId}`
    await client.sAdd(key, sessionId)
  }
  
  static async removeUserSession(userId: string, sessionId: string): Promise<void> {
    const client = getRedisClient()
    const key = `${this.USER_SESSIONS_PREFIX}${userId}`
    await client.sRem(key, sessionId)
  }
  
  static async getUserSessions(userId: string): Promise<string[]> {
    const client = getRedisClient()
    const key = `${this.USER_SESSIONS_PREFIX}${userId}`
    return await client.sMembers(key)
  }
}

// Graceful shutdown
process.on('beforeExit', async () => {
  await disconnectRedis()
})
