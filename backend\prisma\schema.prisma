// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  password  String
  name      String?
  role      UserRole @default(USER)
  tenantId  String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  tenant    Tenant?   @relation(fields: [tenantId], references: [id])
  agents    Agent[]
  sessions  Session[]
  apiKeys   ApiKey[]

  @@map("users")
}

model Tenant {
  id        String   @id @default(cuid())
  name      String
  domain    String?  @unique
  settings  Json?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  users     User[]
  agents    Agent[]
  providers Provider[]

  @@map("tenants")
}

model Agent {
  id          String      @id @default(cuid())
  name        String
  description String?
  config      Json
  status      AgentStatus @default(INACTIVE)
  userId      String
  tenantId    String?
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt

  // Relations
  user      User      @relation(fields: [userId], references: [id])
  tenant    Tenant?   @relation(fields: [tenantId], references: [id])
  sessions  Session[]
  tools     AgentTool[]

  @@map("agents")
}

model Tool {
  id          String     @id @default(cuid())
  name        String
  description String?
  type        ToolType
  config      Json
  schema      Json
  isActive    Boolean    @default(true)
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt

  // Relations
  agents AgentTool[]

  @@map("tools")
}

model AgentTool {
  id       String @id @default(cuid())
  agentId  String
  toolId   String
  config   Json?
  priority Int    @default(0)

  // Relations
  agent Agent @relation(fields: [agentId], references: [id], onDelete: Cascade)
  tool  Tool  @relation(fields: [toolId], references: [id], onDelete: Cascade)

  @@unique([agentId, toolId])
  @@map("agent_tools")
}

model Provider {
  id        String       @id @default(cuid())
  name      String
  type      ProviderType
  config    Json
  isActive  Boolean      @default(true)
  tenantId  String?
  createdAt DateTime     @default(now())
  updatedAt DateTime     @updatedAt

  // Relations
  tenant   Tenant?   @relation(fields: [tenantId], references: [id])
  sessions Session[]

  @@map("providers")
}

model Session {
  id         String        @id @default(cuid())
  userId     String
  agentId    String?
  providerId String?
  context    Json?
  metadata   Json?
  status     SessionStatus @default(ACTIVE)
  expiresAt  DateTime?
  createdAt  DateTime      @default(now())
  updatedAt  DateTime      @updatedAt

  // Relations
  user     User      @relation(fields: [userId], references: [id])
  agent    Agent?    @relation(fields: [agentId], references: [id])
  provider Provider? @relation(fields: [providerId], references: [id])
  events   Event[]

  @@map("sessions")
}

model Event {
  id        String    @id @default(cuid())
  sessionId String
  type      EventType
  data      Json
  timestamp DateTime  @default(now())

  // Relations
  session Session @relation(fields: [sessionId], references: [id], onDelete: Cascade)

  @@map("events")
}

model ApiKey {
  id        String   @id @default(cuid())
  name      String
  key       String   @unique
  userId    String
  scopes    String[]
  expiresAt DateTime?
  lastUsed  DateTime?
  createdAt DateTime @default(now())

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("api_keys")
}

enum UserRole {
  ADMIN
  USER
  DEVELOPER
}

enum AgentStatus {
  ACTIVE
  INACTIVE
  TRAINING
  ERROR
}

enum ToolType {
  INTERNAL
  EXTERNAL
  API
  WEBHOOK
}

enum ProviderType {
  OPENAI
  ANTHROPIC
  GOOGLE
  CUSTOM
}

enum SessionStatus {
  ACTIVE
  INACTIVE
  EXPIRED
  TERMINATED
}

enum EventType {
  AGENT_START
  AGENT_STOP
  TOOL_CALL
  TOOL_RESPONSE
  PROVIDER_REQUEST
  PROVIDER_RESPONSE
  USER_MESSAGE
  AGENT_MESSAGE
  ERROR
  SYSTEM
}
