import winston from 'winston'
import { getEnv } from '@/config/env'

const createLogger = () => {
  const env = getEnv()
  
  const format = winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json(),
    winston.format.printf(({ timestamp, level, message, ...meta }) => {
      return JSON.stringify({
        timestamp,
        level,
        message,
        ...meta
      })
    })
  )

  const transports: winston.transport[] = [
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple()
      )
    })
  ]

  if (env.NODE_ENV === 'production') {
    transports.push(
      new winston.transports.File({
        filename: 'logs/error.log',
        level: 'error'
      }),
      new winston.transports.File({
        filename: 'logs/combined.log'
      })
    )
  }

  return winston.createLogger({
    level: env.LOG_LEVEL,
    format,
    transports,
    exitOnError: false
  })
}

export const logger = createLogger()
