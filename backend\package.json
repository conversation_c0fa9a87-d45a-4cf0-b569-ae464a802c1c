{"name": "@synapseai/backend", "version": "1.0.0", "description": "SynapseAI Backend - AI orchestration API server", "main": "dist/index.js", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:seed": "tsx src/scripts/seed.ts"}, "dependencies": {"express": "^4.18.2", "socket.io": "^4.7.4", "prisma": "^5.7.0", "@prisma/client": "^5.7.0", "redis": "^4.6.10", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "zod": "^3.22.4", "axios": "^1.6.2", "cors": "^2.8.5", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "dotenv": "^16.3.1", "winston": "^3.11.0", "uuid": "^9.0.1", "openai": "^4.20.1", "@anthropic-ai/sdk": "^0.9.1", "@google/generative-ai": "^0.1.3"}, "devDependencies": {"@types/express": "^4.17.21", "@types/node": "^20.10.0", "@types/jsonwebtoken": "^9.0.5", "@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.17", "@types/uuid": "^9.0.7", "@types/jest": "^29.5.8", "@typescript-eslint/eslint-plugin": "^6.13.0", "@typescript-eslint/parser": "^6.13.0", "eslint": "^8.54.0", "jest": "^29.7.0", "ts-jest": "^29.1.1", "tsx": "^4.6.0", "typescript": "^5.3.0"}, "engines": {"node": ">=18.0.0"}}