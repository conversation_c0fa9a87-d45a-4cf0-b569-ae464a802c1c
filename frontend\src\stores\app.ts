import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { ConnectionStatus, UAUIEvent } from '@/types'

interface AppState {
  // UI State
  sidebarOpen: boolean
  theme: 'light' | 'dark' | 'system'
  
  // WebSocket State
  connectionStatus: ConnectionStatus
  recentEvents: UAUIEvent[]
  
  // Navigation State
  currentPage: string
  breadcrumbs: Array<{ label: string; href?: string }>
  
  // Loading States
  globalLoading: boolean
  loadingMessage: string | null
  
  // Notifications
  notifications: Array<{
    id: string
    type: 'info' | 'success' | 'warning' | 'error'
    title: string
    message: string
    timestamp: string
    read: boolean
  }>
  
  // Actions
  setSidebarOpen: (open: boolean) => void
  toggleSidebar: () => void
  setTheme: (theme: 'light' | 'dark' | 'system') => void
  setConnectionStatus: (status: ConnectionStatus) => void
  addEvent: (event: UAUIEvent) => void
  clearEvents: () => void
  setCurrentPage: (page: string) => void
  setBreadcrumbs: (breadcrumbs: Array<{ label: string; href?: string }>) => void
  setGlobalLoading: (loading: boolean, message?: string) => void
  addNotification: (notification: Omit<AppState['notifications'][0], 'id' | 'timestamp' | 'read'>) => void
  markNotificationRead: (id: string) => void
  clearNotifications: () => void
}

export const useAppStore = create<AppState>()(
  persist(
    (set, get) => ({
      // UI State
      sidebarOpen: true,
      theme: 'system',
      
      // WebSocket State
      connectionStatus: {
        connected: false,
        reconnectAttempts: 0
      },
      recentEvents: [],
      
      // Navigation State
      currentPage: '',
      breadcrumbs: [],
      
      // Loading States
      globalLoading: false,
      loadingMessage: null,
      
      // Notifications
      notifications: [],

      // Actions
      setSidebarOpen: (open: boolean) => {
        set({ sidebarOpen: open })
      },

      toggleSidebar: () => {
        set((state) => ({ sidebarOpen: !state.sidebarOpen }))
      },

      setTheme: (theme: 'light' | 'dark' | 'system') => {
        set({ theme })
        
        // Apply theme to document
        const root = document.documentElement
        if (theme === 'dark') {
          root.classList.add('dark')
        } else if (theme === 'light') {
          root.classList.remove('dark')
        } else {
          // System theme
          const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches
          if (prefersDark) {
            root.classList.add('dark')
          } else {
            root.classList.remove('dark')
          }
        }
      },

      setConnectionStatus: (status: ConnectionStatus) => {
        set({ connectionStatus: status })
      },

      addEvent: (event: UAUIEvent) => {
        set((state) => {
          const newEvents = [event, ...state.recentEvents].slice(0, 100) // Keep last 100 events
          return { recentEvents: newEvents }
        })
      },

      clearEvents: () => {
        set({ recentEvents: [] })
      },

      setCurrentPage: (page: string) => {
        set({ currentPage: page })
      },

      setBreadcrumbs: (breadcrumbs: Array<{ label: string; href?: string }>) => {
        set({ breadcrumbs })
      },

      setGlobalLoading: (loading: boolean, message?: string) => {
        set({ 
          globalLoading: loading,
          loadingMessage: loading ? message || null : null
        })
      },

      addNotification: (notification: Omit<AppState['notifications'][0], 'id' | 'timestamp' | 'read'>) => {
        const newNotification = {
          ...notification,
          id: crypto.randomUUID(),
          timestamp: new Date().toISOString(),
          read: false
        }
        
        set((state) => ({
          notifications: [newNotification, ...state.notifications].slice(0, 50) // Keep last 50 notifications
        }))
      },

      markNotificationRead: (id: string) => {
        set((state) => ({
          notifications: state.notifications.map(notification =>
            notification.id === id ? { ...notification, read: true } : notification
          )
        }))
      },

      clearNotifications: () => {
        set({ notifications: [] })
      }
    }),
    {
      name: 'app-storage',
      partialize: (state) => ({
        sidebarOpen: state.sidebarOpen,
        theme: state.theme
      })
    }
  )
)

// Selectors
export const useSidebarOpen = () => useAppStore((state) => state.sidebarOpen)
export const useTheme = () => useAppStore((state) => state.theme)
export const useConnectionStatus = () => useAppStore((state) => state.connectionStatus)
export const useRecentEvents = () => useAppStore((state) => state.recentEvents)
export const useCurrentPage = () => useAppStore((state) => state.currentPage)
export const useBreadcrumbs = () => useAppStore((state) => state.breadcrumbs)
export const useGlobalLoading = () => useAppStore((state) => state.globalLoading)
export const useLoadingMessage = () => useAppStore((state) => state.loadingMessage)
export const useNotifications = () => useAppStore((state) => state.notifications)
export const useUnreadNotifications = () => useAppStore((state) => 
  state.notifications.filter(n => !n.read)
)
