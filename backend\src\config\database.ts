import { PrismaClient } from '@prisma/client'
import { logger } from '@/utils/logger'

declare global {
  var __prisma: PrismaClient | undefined
}

let prisma: PrismaClient

if (process.env.NODE_ENV === 'production') {
  prisma = new PrismaClient({
    log: ['error', 'warn'],
    errorFormat: 'minimal'
  })
} else {
  if (!global.__prisma) {
    global.__prisma = new PrismaClient({
      log: ['query', 'info', 'warn', 'error'],
      errorFormat: 'pretty'
    })
  }
  prisma = global.__prisma
}

export { prisma }

export async function initializeDatabase(): Promise<void> {
  try {
    await prisma.$connect()
    logger.info('✅ Database connected successfully')
    
    // Test the connection
    await prisma.$queryRaw`SELECT 1`
    logger.info('✅ Database health check passed')
    
  } catch (error) {
    logger.error('❌ Database connection failed:', error)
    throw new Error('Failed to connect to database')
  }
}

export async function disconnectDatabase(): Promise<void> {
  try {
    await prisma.$disconnect()
    logger.info('✅ Database disconnected successfully')
  } catch (error) {
    logger.error('❌ Database disconnection failed:', error)
  }
}

// Graceful shutdown
process.on('beforeExit', async () => {
  await disconnectDatabase()
})
